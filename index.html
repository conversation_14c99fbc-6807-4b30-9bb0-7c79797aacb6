
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Handwritten Text to PDF</title>
    <meta name="description" content="Convert your text to handwritten-style PDF" />
    <meta name="author" content="Curator's Lens" />
    
    <!-- Google Fonts for handwriting styles -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&family=Caveat:wght@400;500;600;700&family=Dancing+Script:wght@400;500;600;700&family=Architects+Daughter&family=Indie+Flower&display=swap" rel="stylesheet">

    <meta property="og:title" content="Handwritten Text to PDF" />
    <meta property="og:description" content="Convert your text to handwritten-style PDF" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://curatorslens.com/og-image.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@curatorslens" />
    <meta name="twitter:image" content="https://curatorslens.com/og-image.png" />

    <script type="text/javascript">
    (function(c,l,a,r,i,t,y){
        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
    })(window, document, "clarity", "script", "rtsjuiwyob");
    </script>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
