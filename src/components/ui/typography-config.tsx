import { FC } from 'react';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ColorPicker } from '@/components/ui/color-picker';
import { Card } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Type, Palette, AlignLeft, Hash, RotateCcw } from 'lucide-react';

export interface TypographyConfig {
  fontSize: number;
  lineHeight: number;
  letterSpacing: number;
  wordSpacing: number;
  textColor: string;
  marginTop: number;
  marginBottom: number;
  marginLeft: number;
  marginRight: number;
}

interface TypographyConfigPanelProps {
  config: TypographyConfig;
  onChange: (config: TypographyConfig) => void;
  showPageNumbers: boolean;
  onShowPageNumbersChange: (show: boolean) => void;
  onResetTextPosition?: () => void;
  className?: string;
}

export const TypographyConfigPanel: FC<TypographyConfigPanelProps> = ({
  config,
  onChange,
  showPageNumbers,
  onShowPageNumbersChange,
  onResetTextPosition,
  className
}) => {
  const updateConfig = (key: keyof TypographyConfig, value: any) => {
    onChange({ ...config, [key]: value });
  };

  return (
    <Card className={`p-4 space-y-6 ${className}`}>
      <div className="flex items-center gap-2 mb-4">
        <Type className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
        <h3 className="text-sm font-semibold text-neutral-800 dark:text-neutral-200">
          Typography Settings
        </h3>
      </div>

      {/* Text Styling Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-3">
          <Type className="h-3 w-3 text-neutral-500 dark:text-neutral-400" />
          <h4 className="text-xs font-medium text-neutral-700 dark:text-neutral-300 uppercase tracking-wide">
            Text Styling
          </h4>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Font Size */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Font Size</Label>
              <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                {config.fontSize}px
              </span>
            </div>
            <Slider
              value={[config.fontSize]}
              onValueChange={([value]) => updateConfig('fontSize', value)}
              min={12}
              max={32}
              step={1}
              className="w-full"
            />
          </div>

          {/* Line Height */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Line Height</Label>
              <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                {config.lineHeight}
              </span>
            </div>
            <Slider
              value={[config.lineHeight]}
              onValueChange={([value]) => updateConfig('lineHeight', value)}
              min={1.0}
              max={3.0}
              step={0.1}
              className="w-full"
            />
          </div>

          {/* Letter Spacing */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Letter Spacing</Label>
              <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                {config.letterSpacing}px
              </span>
            </div>
            <Slider
              value={[config.letterSpacing]}
              onValueChange={([value]) => updateConfig('letterSpacing', value)}
              min={-2}
              max={5}
              step={0.1}
              className="w-full"
            />
          </div>

          {/* Word Spacing */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Word Spacing</Label>
              <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                {config.wordSpacing}px
              </span>
            </div>
            <Slider
              value={[config.wordSpacing]}
              onValueChange={([value]) => updateConfig('wordSpacing', value)}
              min={-5}
              max={10}
              step={0.1}
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Ink Color Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-3">
          <Palette className="h-3 w-3 text-neutral-500 dark:text-neutral-400" />
          <h4 className="text-xs font-medium text-neutral-700 dark:text-neutral-300 uppercase tracking-wide">
            Ink Color
          </h4>
        </div>

        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Label className="text-xs font-medium">Text Color</Label>
            <div
              className="w-4 h-4 rounded border border-neutral-300 dark:border-neutral-600"
              style={{ backgroundColor: config.textColor }}
            />
          </div>
          <ColorPicker
            value={config.textColor}
            onChange={(color) => updateConfig('textColor', color)}
            className="w-full"
          />
        </div>
      </div>

      {/* Page Layout Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-3">
          <AlignLeft className="h-3 w-3 text-neutral-500 dark:text-neutral-400" />
          <h4 className="text-xs font-medium text-neutral-700 dark:text-neutral-300 uppercase tracking-wide">
            Page Layout
          </h4>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Top Margin */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Top Margin</Label>
              <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                {config.marginTop}px
              </span>
            </div>
            <Slider
              value={[config.marginTop]}
              onValueChange={([value]) => updateConfig('marginTop', value)}
              min={0}
              max={100}
              step={1}
              className="w-full"
            />
          </div>

          {/* Bottom Margin */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Bottom Margin</Label>
              <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                {config.marginBottom}px
              </span>
            </div>
            <Slider
              value={[config.marginBottom]}
              onValueChange={([value]) => updateConfig('marginBottom', value)}
              min={0}
              max={100}
              step={1}
              className="w-full"
            />
          </div>

          {/* Left Margin */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Left Margin</Label>
              <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                {config.marginLeft}px
              </span>
            </div>
            <Slider
              value={[config.marginLeft]}
              onValueChange={([value]) => updateConfig('marginLeft', value)}
              min={0}
              max={100}
              step={1}
              className="w-full"
            />
          </div>

          {/* Right Margin */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-xs font-medium">Right Margin</Label>
              <span className="text-xs text-neutral-500 bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                {config.marginRight}px
              </span>
            </div>
            <Slider
              value={[config.marginRight]}
              onValueChange={([value]) => updateConfig('marginRight', value)}
              min={0}
              max={100}
              step={1}
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Document Options Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-3">
          <Hash className="h-3 w-3 text-neutral-500 dark:text-neutral-400" />
          <h4 className="text-xs font-medium text-neutral-700 dark:text-neutral-300 uppercase tracking-wide">
            Document Options
          </h4>
        </div>

        <div className="grid grid-cols-1 gap-4">
          {/* Page Numbers */}
          <div className="flex items-center justify-between p-3 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg">
            <div className="flex items-center gap-2">
              <Hash className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
              <div>
                <Label className="text-xs font-medium">Page Numbers</Label>
                <p className="text-xs text-neutral-500">Show page numbers on multi-page documents</p>
              </div>
            </div>
            <Switch
              checked={showPageNumbers}
              onCheckedChange={onShowPageNumbersChange}
            />
          </div>

          {/* Text Position Reset */}
          {onResetTextPosition && (
            <div className="flex items-center justify-between p-3 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg">
              <div className="flex items-center gap-2">
                <RotateCcw className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
                <div>
                  <Label className="text-xs font-medium">Text Position</Label>
                  <p className="text-xs text-neutral-500">
                    Drag text in preview to reposition
                  </p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={onResetTextPosition}
                className="h-8 px-3 text-xs"
              >
                Reset
              </Button>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};
