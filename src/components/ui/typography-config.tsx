import { FC } from 'react';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ColorPicker } from '@/components/ui/color-picker';
import { Card } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Type, Palette, AlignLeft, Hash, RotateCcw } from 'lucide-react';

export interface TypographyConfig {
  fontSize: number;
  lineHeight: number;
  letterSpacing: number;
  wordSpacing: number;
  textColor: string;
  marginTop: number;
  marginBottom: number;
  marginLeft: number;
  marginRight: number;
}

interface TypographyConfigPanelProps {
  config: TypographyConfig;
  onChange: (config: TypographyConfig) => void;
  showPageNumbers: boolean;
  onShowPageNumbersChange: (show: boolean) => void;
  onResetTextPosition?: () => void;
  className?: string;
}

export const TypographyConfigPanel: FC<TypographyConfigPanelProps> = ({
  config,
  onChange,
  showPageNumbers,
  onShowPageNumbersChange,
  onResetTextPosition,
  className
}) => {
  const updateConfig = (key: keyof TypographyConfig, value: any) => {
    onChange({ ...config, [key]: value });
  };

  return (
    <Card className={`p-4 space-y-4 ${className}`}>
      <div className="flex items-center gap-2 mb-3">
        <Type className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
        <h3 className="text-sm font-semibold text-neutral-800 dark:text-neutral-200">
          Typography Settings
        </h3>
      </div>

      {/* Font Size */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label className="text-xs font-medium">Font Size</Label>
          <span className="text-xs text-neutral-500">{config.fontSize}px</span>
        </div>
        <Slider
          value={[config.fontSize]}
          onValueChange={([value]) => updateConfig('fontSize', value)}
          min={12}
          max={32}
          step={1}
          className="w-full"
        />
      </div>

      {/* Line Height */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label className="text-xs font-medium">Line Height</Label>
          <span className="text-xs text-neutral-500">{config.lineHeight}</span>
        </div>
        <Slider
          value={[config.lineHeight]}
          onValueChange={([value]) => updateConfig('lineHeight', value)}
          min={1.0}
          max={3.0}
          step={0.1}
          className="w-full"
        />
      </div>

      {/* Letter Spacing */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label className="text-xs font-medium">Letter Spacing</Label>
          <span className="text-xs text-neutral-500">{config.letterSpacing}px</span>
        </div>
        <Slider
          value={[config.letterSpacing]}
          onValueChange={([value]) => updateConfig('letterSpacing', value)}
          min={-2}
          max={5}
          step={0.1}
          className="w-full"
        />
      </div>

      {/* Word Spacing */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label className="text-xs font-medium">Word Spacing</Label>
          <span className="text-xs text-neutral-500">{config.wordSpacing}px</span>
        </div>
        <Slider
          value={[config.wordSpacing]}
          onValueChange={([value]) => updateConfig('wordSpacing', value)}
          min={-5}
          max={10}
          step={0.1}
          className="w-full"
        />
      </div>

      {/* Text Color */}
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Palette className="h-3 w-3 text-neutral-600 dark:text-neutral-400" />
          <Label className="text-xs font-medium">Ink Color</Label>
        </div>
        <ColorPicker
          value={config.textColor}
          onChange={(color) => updateConfig('textColor', color)}
          className="w-full"
        />
      </div>

      {/* Page Margins */}
      <div className="space-y-3">
        <div className="flex items-center gap-2 mb-2">
          <AlignLeft className="h-3 w-3 text-neutral-600 dark:text-neutral-400" />
          <Label className="text-xs font-medium">Page Margins</Label>
        </div>

        <div className="grid grid-cols-2 gap-3">
          {/* Top Margin */}
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Top</Label>
              <span className="text-xs text-neutral-500">{config.marginTop}px</span>
            </div>
            <Slider
              value={[config.marginTop]}
              onValueChange={([value]) => updateConfig('marginTop', value)}
              min={0}
              max={100}
              step={1}
              className="w-full"
            />
          </div>

          {/* Bottom Margin */}
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Bottom</Label>
              <span className="text-xs text-neutral-500">{config.marginBottom}px</span>
            </div>
            <Slider
              value={[config.marginBottom]}
              onValueChange={([value]) => updateConfig('marginBottom', value)}
              min={0}
              max={100}
              step={1}
              className="w-full"
            />
          </div>

          {/* Left Margin */}
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Left</Label>
              <span className="text-xs text-neutral-500">{config.marginLeft}px</span>
            </div>
            <Slider
              value={[config.marginLeft]}
              onValueChange={([value]) => updateConfig('marginLeft', value)}
              min={0}
              max={100}
              step={1}
              className="w-full"
            />
          </div>

          {/* Right Margin */}
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Right</Label>
              <span className="text-xs text-neutral-500">{config.marginRight}px</span>
            </div>
            <Slider
              value={[config.marginRight]}
              onValueChange={([value]) => updateConfig('marginRight', value)}
              min={0}
              max={100}
              step={1}
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Page Numbers */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Hash className="h-3 w-3 text-neutral-600 dark:text-neutral-400" />
            <Label className="text-xs font-medium">Show Page Numbers</Label>
          </div>
          <Switch
            checked={showPageNumbers}
            onCheckedChange={onShowPageNumbersChange}
          />
        </div>
      </div>

      {/* Text Position Reset */}
      {onResetTextPosition && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <RotateCcw className="h-3 w-3 text-neutral-600 dark:text-neutral-400" />
              <Label className="text-xs font-medium">Text Position</Label>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={onResetTextPosition}
              className="h-6 px-2 text-xs"
            >
              Reset
            </Button>
          </div>
          <p className="text-xs text-neutral-500">
            Drag text in preview to reposition. Click reset to center.
          </p>
        </div>
      )}
    </Card>
  );
};
