import { FC, Fragment } from 'react';
import 'katex/dist/katex.min.css';
import katex from 'katex';

interface LaTeXRendererProps {
  text: string;
  className?: string;
  style?: React.CSSProperties;
}

export const LaTeXRenderer: FC<LaTeXRendererProps> = ({
  text,
  className = '',
  style = {},
  ...otherProps
}) => {
  // Filter out any invalid props that shouldn't be passed to DOM elements
  const validProps = Object.fromEntries(
    Object.entries(otherProps).filter(([key]) =>
      !key.startsWith('data-lov') && !key.startsWith('data-testid')
    )
  );
  const renderText = (input: string) => {
    if (!input) return [];

    // Split text by LaTeX delimiters
    // Support both $...$ (inline) and $$...$$ (display) math
    const parts = input.split(/(\$\$[\s\S]*?\$\$|\$[\s\S]*?\$)/);

    return parts.map((part, index) => {
      if (part.startsWith('$$') && part.endsWith('$$')) {
        // Display math (block)
        const mathContent = part.slice(2, -2);
        try {
          const html = katex.renderToString(mathContent, {
            displayMode: true,
            throwOnError: false,
            errorColor: '#cc0000',
            strict: false
          });
          return (
            <div
              key={index}
              className="my-2 text-center katex-display-wrapper"
              dangerouslySetInnerHTML={{ __html: html }}
              style={{
                // Apply text color to math elements
                color: style.color || 'inherit'
              }}
            />
          );
        } catch (error) {
          return (
            <div key={index} className="text-red-500 text-sm my-2">
              LaTeX Error: {mathContent}
            </div>
          );
        }
      } else if (part.startsWith('$') && part.endsWith('$')) {
        // Inline math
        const mathContent = part.slice(1, -1);
        try {
          const html = katex.renderToString(mathContent, {
            displayMode: false,
            throwOnError: false,
            errorColor: '#cc0000',
            strict: false
          });
          return (
            <span
              key={index}
              className="katex-inline-wrapper"
              dangerouslySetInnerHTML={{ __html: html }}
              style={{
                // Apply text color to math elements
                color: style.color || 'inherit'
              }}
            />
          );
        } catch (error) {
          return (
            <span key={index} className="text-red-500 text-sm">
              LaTeX Error: {mathContent}
            </span>
          );
        }
      } else {
        // Regular text - preserve line breaks and apply handwriting font
        return part.split('\n').map((line, lineIndex, lines) => (
          <Fragment key={`${index}-${lineIndex}`}>
            {line}
            {lineIndex < lines.length - 1 && <br />}
          </Fragment>
        ));
      }
    });
  };

  // Extract font family from className
  const fontFamily = className.includes('font-handwriting-alt') ? 'Caveat, cursive' :
                    className.includes('font-handwriting-dance') ? 'Dancing Script, cursive' :
                    className.includes('font-handwriting-architect') ? 'Architects Daughter, cursive' :
                    className.includes('font-handwriting-indie') ? 'Indie Flower, cursive' :
                    className.includes('font-handwriting') ? 'Kalam, cursive' : 'inherit';

  return (
    <div
      className={className}
      style={{
        ...style,
        // Apply color and font to KaTeX elements using CSS custom properties
        '--katex-color': style.color || 'inherit',
        '--katex-font': fontFamily
      }}
    >
      <style dangerouslySetInnerHTML={{
        __html: `
          .katex-display-wrapper .katex,
          .katex-inline-wrapper .katex {
            color: var(--katex-color) !important;
            font-family: var(--katex-font) !important;
          }
          .katex-display-wrapper .katex .mord,
          .katex-inline-wrapper .katex .mord,
          .katex-display-wrapper .katex .mop,
          .katex-inline-wrapper .katex .mop,
          .katex-display-wrapper .katex .mbin,
          .katex-inline-wrapper .katex .mbin,
          .katex-display-wrapper .katex .mrel,
          .katex-inline-wrapper .katex .mrel,
          .katex-display-wrapper .katex .mopen,
          .katex-inline-wrapper .katex .mopen,
          .katex-display-wrapper .katex .mclose,
          .katex-inline-wrapper .katex .mclose,
          .katex-display-wrapper .katex .mpunct,
          .katex-inline-wrapper .katex .mpunct,
          .katex-display-wrapper .katex .msupsub,
          .katex-inline-wrapper .katex .msupsub,
          .katex-display-wrapper .katex .mfrac,
          .katex-inline-wrapper .katex .mfrac,
          .katex-display-wrapper .katex .mroot,
          .katex-inline-wrapper .katex .mroot,
          .katex-display-wrapper .katex .msqrt,
          .katex-inline-wrapper .katex .msqrt {
            color: var(--katex-color) !important;
            font-family: var(--katex-font) !important;
          }
          .katex-display-wrapper .katex .katex-html,
          .katex-inline-wrapper .katex .katex-html {
            font-family: var(--katex-font) !important;
          }
          .katex-display-wrapper .katex .base,
          .katex-inline-wrapper .katex .base {
            font-family: var(--katex-font) !important;
          }
        `
      }} />
      {renderText(text)}
    </div>
  );
};

// Helper function to check if text contains LaTeX
export const hasLaTeX = (text: string): boolean => {
  return /\$[\s\S]*?\$|\$\$[\s\S]*?\$\$/.test(text);
};

// Helper function to validate LaTeX syntax
export const validateLaTeX = (text: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  const mathParts = text.match(/\$\$[\s\S]*?\$\$|\$[\s\S]*?\$/g) || [];
  
  mathParts.forEach((part, index) => {
    const mathContent = part.startsWith('$$') 
      ? part.slice(2, -2) 
      : part.slice(1, -1);
    
    try {
      katex.renderToString(mathContent, {
        displayMode: part.startsWith('$$'),
        throwOnError: true,
        strict: false
      });
    } catch (error) {
      errors.push(`Math expression ${index + 1}: ${error.message}`);
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors
  };
};
