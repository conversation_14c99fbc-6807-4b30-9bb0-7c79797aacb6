import { FC, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { BackgroundGallery } from '@/components/ui/background-gallery';
import { TypographyConfigPanel, TypographyConfig } from '@/components/ui/typography-config';
import { 
  Download, 
  Type, 
  Palette, 
  Image, 
  Settings, 
  ChevronDown, 
  ChevronUp,
  FileText,
  Brush
} from 'lucide-react';

interface FontOption {
  value: string;
  label: string;
  class: string;
}

interface EnhancedToolbarProps {
  // Font settings
  selectedFont: string;
  onFontChange: (font: string) => void;
  fontOptions: FontOption[];
  
  // Background settings
  selectedBackground: string;
  onBackgroundChange: (background: string) => void;
  customBackground: string;
  onCustomBackgroundUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  
  // Typography settings
  typographyConfig: TypographyConfig;
  onTypographyChange: (config: TypographyConfig) => void;
  showPageNumbers: boolean;
  onShowPageNumbersChange: (show: boolean) => void;
  onResetTextPosition: () => void;
  
  // Actions
  onGeneratePDF: () => void;
  onGenerateImage: () => void;
  hasText: boolean;
  
  className?: string;
}

export const EnhancedToolbar: FC<EnhancedToolbarProps> = ({
  selectedFont,
  onFontChange,
  fontOptions,
  selectedBackground,
  onBackgroundChange,
  customBackground,
  onCustomBackgroundUpload,
  typographyConfig,
  onTypographyChange,
  showPageNumbers,
  onShowPageNumbersChange,
  onResetTextPosition,
  onGeneratePDF,
  onGenerateImage,
  hasText,
  className
}) => {
  const [isBackgroundOpen, setIsBackgroundOpen] = useState(false);
  const [isTypographyOpen, setIsTypographyOpen] = useState(false);

  return (
    <Card className={`p-6 bg-white/80 dark:bg-black/40 backdrop-blur-md border border-neutral-200/20 dark:border-neutral-800/20 shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset] dark:shadow-[0_0_24px_rgba(0,_0,_0,_0.3)] ${className}`}>
      <div className="space-y-6">
        {/* Main Controls Row */}
        <div className="flex flex-col lg:flex-row gap-4 lg:items-center lg:justify-between">
          {/* Font Selection */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-3 min-w-0">
            <div className="flex items-center gap-2 shrink-0">
              <Type className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
              <span className="text-sm font-medium text-neutral-700 dark:text-neutral-300">Font:</span>
            </div>
            <Select value={selectedFont} onValueChange={onFontChange}>
              <SelectTrigger className="w-full sm:w-48 bg-white/50 dark:bg-black/50 border-neutral-200/20 dark:border-neutral-700/20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {fontOptions.map((font) => (
                  <SelectItem key={font.value} value={font.value}>
                    <span className={font.class}>{font.label}</span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-2 shrink-0">
            <Button
              onClick={onGenerateImage}
              disabled={!hasText}
              variant="outline"
              className="w-full sm:w-auto bg-white/50 dark:bg-black/50 border-neutral-200/20 dark:border-neutral-700/20"
            >
              <Image className="h-4 w-4 mr-2" />
              <span className="sm:hidden">Export as </span>Image
            </Button>

            <button
              onClick={onGeneratePDF}
              disabled={!hasText}
              className="w-full sm:w-auto px-6 sm:px-8 py-2 sm:py-0.5 border-2 border-black dark:border-white uppercase bg-white text-black transition duration-200 text-sm shadow-[1px_1px_rgba(0,0,0),2px_2px_rgba(0,0,0),3px_3px_rgba(0,0,0),4px_4px_rgba(0,0,0),5px_5px_0px_0px_rgba(0,0,0)] dark:shadow-[1px_1px_rgba(255,255,255),2px_2px_rgba(255,255,255),3px_3px_rgba(255,255,255),4px_4px_rgba(255,255,255),5px_5px_0px_0px_rgba(255,255,255)] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span className="sm:hidden">Export as </span>PDF
              <Download className="h-4 w-4 inline-block ml-1" />
            </button>
          </div>
        </div>

        {/* Collapsible Sections */}
        <div className="space-y-4">
          {/* Background Selection */}
          <Collapsible open={isBackgroundOpen} onOpenChange={setIsBackgroundOpen}>
            <CollapsibleTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-between bg-white/50 dark:bg-black/50 border-neutral-200/20 dark:border-neutral-700/20 hover:bg-white/70 dark:hover:bg-black/60"
              >
                <div className="flex items-center gap-2">
                  <Palette className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
                  <span className="font-medium">Background & Paper Style</span>
                  <span className="text-sm text-neutral-500 ml-2">
                    ({selectedBackground === 'custom' ? 'Custom Image' : 
                      selectedBackground.charAt(0).toUpperCase() + selectedBackground.slice(1).replace('-', ' ')})
                  </span>
                </div>
                {isBackgroundOpen ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-4">
              <div className="p-4 bg-white/30 dark:bg-black/30 rounded-lg border border-neutral-200/20 dark:border-neutral-700/20">
                <BackgroundGallery
                  selectedBackground={selectedBackground}
                  onBackgroundChange={onBackgroundChange}
                  customBackground={customBackground}
                  onCustomBackgroundUpload={onCustomBackgroundUpload}
                />
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* Typography Settings */}
          <Collapsible open={isTypographyOpen} onOpenChange={setIsTypographyOpen}>
            <CollapsibleTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-between bg-white/50 dark:bg-black/50 border-neutral-200/20 dark:border-neutral-700/20 hover:bg-white/70 dark:hover:bg-black/60"
              >
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4 text-neutral-600 dark:text-neutral-400" />
                  <span className="font-medium">Typography & Layout</span>
                  <span className="text-sm text-neutral-500 ml-2">
                    ({typographyConfig.fontSize}px, {typographyConfig.textColor})
                  </span>
                </div>
                {isTypographyOpen ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-4">
              <div className="p-4 bg-white/30 dark:bg-black/30 rounded-lg border border-neutral-200/20 dark:border-neutral-700/20">
                <TypographyConfigPanel
                  config={typographyConfig}
                  onChange={onTypographyChange}
                  showPageNumbers={showPageNumbers}
                  onShowPageNumbersChange={onShowPageNumbersChange}
                  onResetTextPosition={onResetTextPosition}
                  className="bg-transparent border-none shadow-none p-0"
                />
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>

        {/* Quick Status Bar */}
        <div className="flex items-center justify-between text-xs text-neutral-500 dark:text-neutral-400 pt-2 border-t border-neutral-200/20 dark:border-neutral-700/20">
          <div className="flex items-center gap-4">
            <span>Font: {fontOptions.find(f => f.value === selectedFont)?.label}</span>
            <span>Background: {selectedBackground === 'custom' ? 'Custom' : selectedBackground.replace('-', ' ')}</span>
          </div>
          <div className="flex items-center gap-2">
            <FileText className="h-3 w-3" />
            <span>Ready to export</span>
          </div>
        </div>
      </div>
    </Card>
  );
};
