import { FC } from 'react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';

interface ColorPickerProps {
  value: string;
  onChange: (color: string) => void;
  className?: string;
}

const predefinedColors = [
  { name: 'Black Ink', value: '#000000' },
  { name: 'Blue Ink', value: '#1e40af' },
  { name: 'Navy Blue', value: '#1e3a8a' },
  { name: 'Royal Blue', value: '#2563eb' },
  { name: 'Red Ink', value: '#dc2626' },
  { name: 'Green Ink', value: '#16a34a' },
  { name: 'Purple Ink', value: '#7c3aed' },
  { name: 'Brown Ink', value: '#a16207' },
  { name: 'Gray Ink', value: '#4b5563' },
  { name: '<PERSON> Gray', value: '#374151' },
];

export const ColorPicker: FC<ColorPickerProps> = ({
  value,
  onChange,
  className
}) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-[100px] h-8 p-1 border border-neutral-200 dark:border-neutral-700",
            className
          )}
        >
          <div className="flex items-center gap-2 w-full">
            <div
              className="w-4 h-4 rounded border border-neutral-300 dark:border-neutral-600"
              style={{ backgroundColor: value }}
            />
            <span className="text-xs truncate">Color</span>
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64 p-3">
        <div className="space-y-3">
          <h4 className="text-sm font-medium">Ink Colors</h4>
          <div className="grid grid-cols-5 gap-2">
            {predefinedColors.map((color) => (
              <button
                key={color.value}
                className={cn(
                  "w-8 h-8 rounded border-2 transition-all hover:scale-110",
                  value === color.value
                    ? "border-neutral-900 dark:border-neutral-100"
                    : "border-neutral-300 dark:border-neutral-600"
                )}
                style={{ backgroundColor: color.value }}
                onClick={() => onChange(color.value)}
                title={color.name}
              />
            ))}
          </div>
          <div className="space-y-2">
            <label className="text-xs font-medium">Custom Color</label>
            <input
              type="color"
              value={value}
              onChange={(e) => onChange(e.target.value)}
              className="w-full h-8 rounded border border-neutral-300 dark:border-neutral-600 cursor-pointer"
            />
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};
