"use client";
import { motion } from "motion/react";
import { HeroHighlight, Highlight } from "@/components/ui/hero-highlight";

export default function HeroHighlightDemo() {
            {/* Header */}
        // <div className="text-center mb-8">
        //   <h1 className="text-4xl font-bold text-gray-800 mb-2 flex items-center justify-center gap-3">
        //     <FileText className="h-8 w-8 text-blue-600" />
        //     Handwritten Text to PDF
        //   </h1>
        //   <p className="text-gray-600">Write your text and convert it to a handwritten-style PDF</p>
        // </div>
  return (
    <HeroHighlight>
      <motion.h1
        initial={{
          opacity: 0,
          y: 20,
        }}
        animate={{
          opacity: 1,
          y: [20, -5, 0],
        }}
        transition={{
          duration: 0.5,
          ease: [0.4, 0.0, 0.2, 1],
        }}
        className="text-2xl px-4 md:text-4xl lg:text-5xl font-bold text-neutral-700 dark:text-white max-w-4xl leading-relaxed lg:leading-snug text-center mx-auto "
      >
        <Highlight className="text-black dark:text-white">
          Handwritten 
        </Highlight> Text to PDF{" "}
      </motion.h1>
    </HeroHighlight>
  );
}
